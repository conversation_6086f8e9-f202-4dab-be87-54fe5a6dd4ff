import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function createBrowserTicket() {
  try {
    console.log('🎫 Criando bilhete que o browser está esperando...')
    
    await initializeDatabase()
    
    // Dados exatos do bilhete que o browser está procurando
    const bilheteData = {
      codigo: 'BLT175298694463225ZO3O7R',
      transaction_id: 'pixi_01k0k2m021eh08303b6hb0bh37',
      user_id: 25, // Vamos usar um user_id diferente
      valor: 0.1,
      status: 'pendente'
    }
    
    console.log('📤 Criando bilhete:', bilheteData)
    
    // Verificar se já existe
    const existente = await executeQuery(`
      SELECT id FROM bilhetes WHERE codigo = ? OR transaction_id = ?
    `, [bilheteData.codigo, bilheteData.transaction_id])
    
    if (existente.length > 0) {
      console.log('⚠️ Bilhete já existe, atualizando...')
      
      await executeQuery(`
        UPDATE bilhetes 
        SET status = ?, valor_total = ?
        WHERE codigo = ? OR transaction_id = ?
      `, [bilheteData.status, bilheteData.valor, bilheteData.codigo, bilheteData.transaction_id])
      
      console.log('✅ Bilhete atualizado')
    } else {
      console.log('➕ Criando novo bilhete...')
      
      // Criar bilhete
      const result = await executeQuery(`
        INSERT INTO bilhetes (
          codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
          valor_total, quantidade_apostas, status, transaction_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        bilheteData.codigo,
        bilheteData.user_id,
        'Usuário Teste',
        '<EMAIL>',
        '12345678901',
        bilheteData.valor,
        11, // 11 apostas
        bilheteData.status,
        bilheteData.transaction_id
      ])
      
      console.log(`✅ Bilhete criado com ID: ${result.insertId}`)
    }
    
    // Verificar se foi criado/atualizado
    const bilheteCriado = await executeQuery(`
      SELECT * FROM bilhetes WHERE codigo = ?
    `, [bilheteData.codigo])
    
    if (bilheteCriado.length > 0) {
      const bilhete = bilheteCriado[0]
      console.log('✅ Bilhete confirmado:', {
        id: bilhete.id,
        codigo: bilhete.codigo,
        usuario_nome: bilhete.usuario_nome,
        valor_total: bilhete.valor_total,
        status: bilhete.status,
        transaction_id: bilhete.transaction_id
      })
      
      console.log('\n🧪 Para marcar como pago, use:')
      console.log(`curl -X POST "http://localhost:3000/api/v1/MP/webhookruntransation" -H "Content-Type: application/json" -d "{\\"order_id\\": \\"${bilhete.codigo}\\", \\"status\\": \\"PAID\\", \\"type\\": \\"PIXOUT\\", \\"message\\": \\"Payment approved\\"}"`)
    }
    
  } catch (error) {
    console.error('❌ Erro ao criar bilhete do browser:', error)
  }
}

createBrowserTicket()
