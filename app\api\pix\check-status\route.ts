import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const transaction_id = searchParams.get("transaction_id")

    console.log("🔍 Verificando status PIX para transaction_id:", transaction_id)

    if (!transaction_id) {
      return NextResponse.json({
        success: false,
        error: "transaction_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Buscar bilhete no banco de dados
    const bilhetes = await executeQuery(`
      SELECT 
        id, codigo, transaction_id, status, valor_total, 
        created_at, updated_at, pix_order_id
      FROM bilhetes 
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [transaction_id, transaction_id])

    if (bilhetes.length === 0) {
      console.log("❌ Bilhete não encontrado para transaction_id:", transaction_id)
      return NextResponse.json({
        success: false,
        error: "Bilhete não encontrado",
        status: "not_found"
      }, { status: 404 })
    }

    const bilhete = bilhetes[0]
    console.log("📋 Bilhete encontrado:", {
      id: bilhete.id,
      codigo: bilhete.codigo,
      status: bilhete.status,
      valor: bilhete.valor_total
    })

    // Verificação com provedor PIX real ou simulação
    let statusAtualizado = bilhete.status
    let shouldUpdate = false
    let providerResponse = null

    try {
      // OPÇÃO 1: Integração real com PicPay (descomente para usar)
      /*
      if (bilhete.pix_order_id) {
        const picpayResponse = await fetch(`https://appws.picpay.com/ecommerce/public/payments/${bilhete.pix_order_id}/status`, {
          method: 'GET',
          headers: {
            'x-picpay-token': process.env.PICPAY_TOKEN || '',
            'Content-Type': 'application/json'
          }
        })

        if (picpayResponse.ok) {
          providerResponse = await picpayResponse.json()
          console.log("🏦 Resposta PicPay:", providerResponse)

          if (providerResponse.status === 'paid') {
            statusAtualizado = 'pago'
            shouldUpdate = true
          }
        }
      }
      */

      // OPÇÃO 2: Integração com Mercado Pago (descomente para usar)
      /*
      if (bilhete.transaction_id && bilhete.transaction_id.startsWith('mp_')) {
        const mpResponse = await fetch(`https://api.mercadopago.com/v1/payments/${bilhete.transaction_id}`, {
          headers: {
            'Authorization': `Bearer ${process.env.MERCADO_PAGO_ACCESS_TOKEN}`,
            'Content-Type': 'application/json'
          }
        })

        if (mpResponse.ok) {
          providerResponse = await mpResponse.json()
          console.log("💳 Resposta Mercado Pago:", providerResponse)

          if (providerResponse.status === 'approved') {
            statusAtualizado = 'pago'
            shouldUpdate = true
          }
        }
      }
      */

      // SIMULAÇÃO para demonstração (remover em produção)
      const agora = new Date()
      const criadoEm = new Date(bilhete.created_at)
      const tempoDecorrido = (agora.getTime() - criadoEm.getTime()) / 1000

      if (bilhete.status === 'pendente' && tempoDecorrido > 30) {
        console.log("🎯 Simulando pagamento aprovado (mais de 30 segundos)")
        statusAtualizado = 'pago'
        shouldUpdate = true
        providerResponse = {
          status: 'paid',
          simulation: true,
          tempo_decorrido: tempoDecorrido
        }
      }

    } catch (providerError) {
      console.log("⚠️ Erro ao consultar provedor PIX:", providerError)
    }

    // Se o status mudou, atualizar no banco
    if (shouldUpdate) {
      console.log("💾 Atualizando status do bilhete para:", statusAtualizado)
      
      await executeQuery(`
        UPDATE bilhetes 
        SET status = ?, updated_at = NOW() 
        WHERE id = ?
      `, [statusAtualizado, bilhete.id])

      // Disparar evento personalizado para notificar o frontend
      console.log("📡 Status atualizado com sucesso")
    }

    const agora = new Date()
    const criadoEm = new Date(bilhete.created_at)
    const tempoDecorrido = (agora.getTime() - criadoEm.getTime()) / 1000

    return NextResponse.json({
      success: true,
      transaction_id: transaction_id,
      bilhete_id: bilhete.id,
      codigo: bilhete.codigo,
      status: statusAtualizado,
      status_anterior: bilhete.status,
      valor: bilhete.valor_total,
      updated: shouldUpdate,
      tempo_decorrido: Math.round(tempoDecorrido),
      provider_response: providerResponse,
      message: shouldUpdate ? "Status atualizado automaticamente" : "Status inalterado",
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro ao verificar status PIX:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
