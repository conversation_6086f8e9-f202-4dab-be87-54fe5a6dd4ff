// Script para verificar se a troca de domínio foi feita corretamente
import fs from 'fs'
import path from 'path'

console.log('🔍 VERIFICANDO TROCA DE DOMÍNIO')
console.log('=' .repeat(50))
console.log('🎯 Novo domínio: https://joanadeoxum.com')
console.log('🔄 Antigo domínio: https://ouroemu.site')
console.log('')

// Arquivos para verificar
const filesToCheck = [
  '.env.local',
  'next.config.mjs',
  'config/webhook.config.js',
  'app/api/webhook-config/route.ts',
  'app/api/pix/qrcode/route.ts',
  'lib/pix-api.js',
  'docs/PIX-API-INTEGRATION.md',
  'scripts/verify-domain-config.js',
  'app/api/pix/diagnostico/route.ts'
]

let allCorrect = true
let issues = []

// Verificar cada arquivo
filesToCheck.forEach(filePath => {
  console.log(`📄 Verificando: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ⚠️  Arquivo não encontrado`)
    issues.push(`Arquivo ${filePath} não encontrado`)
    allCorrect = false
    return
  }

  const content = fs.readFileSync(filePath, 'utf8')
  
  // Verificar se contém o novo domínio
  const hasNewDomain = content.includes('joanadeoxum.com')
  
  // Verificar se ainda contém o domínio antigo (exceto em listas de domínios antigos)
  const hasOldDomain = content.includes('ouroemu.site')
  const isOldDomainInList = filePath.includes('verify-domain-config.js') && content.includes('OLD_DOMAINS')

  if (hasNewDomain) {
    console.log(`   ✅ Contém novo domínio (joanadeoxum.com)`)
  } else {
    console.log(`   ❌ NÃO contém novo domínio`)
    issues.push(`${filePath} não contém joanadeoxum.com`)
    allCorrect = false
  }

  if (hasOldDomain && !isOldDomainInList) {
    console.log(`   ⚠️  Ainda contém domínio antigo (ouroemu.site)`)
    issues.push(`${filePath} ainda contém ouroemu.site`)
    allCorrect = false
  } else if (hasOldDomain && isOldDomainInList) {
    console.log(`   ✅ Contém domínio antigo apenas na lista de domínios antigos (OK)`)
  } else {
    console.log(`   ✅ Não contém domínio antigo`)
  }

  console.log('')
})

// Verificar variáveis de ambiente específicas
console.log('🔧 VERIFICANDO VARIÁVEIS DE AMBIENTE')
console.log('=' .repeat(50))

if (fs.existsSync('.env.local')) {
  console.log('📄 .env.local:')
  const content = fs.readFileSync('.env.local', 'utf8')
  
  // Variáveis importantes
  const importantVars = [
    'PIX_WEBHOOK_URL',
    'EMAIL_SUPORTE',
    'NEXT_PUBLIC_APP_URL'
  ]

  importantVars.forEach(varName => {
    // Buscar apenas linhas não comentadas
    const regex = new RegExp(`^${varName}=(.+)`, 'm')
    const match = content.match(regex)

    if (match) {
      const value = match[1].trim()
      const isCorrect = value.includes('joanadeoxum.com') || value.includes('localhost')
      console.log(`   ${varName}: ${value} ${isCorrect ? '✅' : '❌'}`)

      if (!isCorrect) {
        issues.push(`.env.local: ${varName} não está configurado para joanadeoxum.com`)
        allCorrect = false
      }
    } else {
      console.log(`   ${varName}: NÃO ENCONTRADO ❌`)
      issues.push(`.env.local: ${varName} não encontrado`)
      allCorrect = false
    }
  })
  console.log('')
}

// Resultado final
console.log('📊 RESULTADO DA VERIFICAÇÃO')
console.log('=' .repeat(50))

if (allCorrect) {
  console.log('✅ SUCESSO! Todos os arquivos foram atualizados corretamente')
  console.log('🎉 Domínio alterado com sucesso para: https://joanadeoxum.com')
} else {
  console.log('❌ PROBLEMAS ENCONTRADOS:')
  issues.forEach(issue => {
    console.log(`   - ${issue}`)
  })
  
  console.log('')
  console.log('🔧 CORREÇÕES NECESSÁRIAS:')
  console.log('- Substitua todas as ocorrências de ouroemu.site por joanadeoxum.com')
  console.log('- Verifique as variáveis de ambiente')
  console.log('- Execute este script novamente após as correções')
}

console.log('')
console.log('🌐 URLs importantes após deploy:')
console.log('- Site principal: https://joanadeoxum.com')
console.log('- Painel admin: https://joanadeoxum.com/admin')
console.log('- Dashboard cambistas: https://joanadeoxum.com/cambistas')
console.log('- Webhook PIX: https://joanadeoxum.com/api/v1/MP/webhookruntransation')
console.log('- Configuração webhook: https://joanadeoxum.com/api/webhook-config')

console.log('')
console.log('📋 PRÓXIMOS PASSOS:')
console.log('1. Fazer deploy do sistema no novo domínio')
console.log('2. Configurar DNS para apontar para o servidor')
console.log('3. Configurar SSL/HTTPS no novo domínio')
console.log('4. Testar todas as funcionalidades')
console.log('5. Atualizar configurações da API PIX no provedor')

process.exit(allCorrect ? 0 : 1)
