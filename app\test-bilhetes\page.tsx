"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function TestBilhetesPage() {
  const [bilhetes, setBilhetes] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userId, setUserId] = useState<string>("18") // ID de teste

  const testFetchBilhetes = async () => {
    setLoading(true)
    setError(null)
    
    try {
      console.log("🔍 Testando fetch de bilhetes para userId:", userId)
      
      const response = await fetch(`/api/user/bilhetes?user_id=${userId}`)
      const data = await response.json()
      
      console.log("📊 Resposta da API:", data)
      console.log("📊 Status da resposta:", response.status)
      console.log("📊 Bilhetes recebidos:", data.bilhetes)
      
      if (data.success) {
        setBilhetes(data.bilhetes || [])
      } else {
        setError(data.error || "Erro desconhecido")
      }
    } catch (err) {
      console.error("❌ Erro ao buscar bilhetes:", err)
      setError(err instanceof Error ? err.message : "Erro desconhecido")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    testFetchBilhetes()
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Teste de Bilhetes</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <div className="flex gap-4 items-center mb-4">
            <input
              type="text"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="User ID"
              className="border p-2 rounded"
            />
            <Button onClick={testFetchBilhetes} disabled={loading}>
              {loading ? "Carregando..." : "Buscar Bilhetes"}
            </Button>
          </div>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              Erro: {error}
            </div>
          )}
          
          <div className="mb-4">
            <h2 className="text-xl font-semibold mb-2">Informações de Debug:</h2>
            <div className="bg-gray-100 p-4 rounded">
              <p><strong>Total de bilhetes:</strong> {bilhetes.length}</p>
              <p><strong>Loading:</strong> {loading ? "Sim" : "Não"}</p>
              <p><strong>Error:</strong> {error || "Nenhum"}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Bilhetes Encontrados:</h2>
          
          {bilhetes.length === 0 ? (
            <p className="text-gray-500">Nenhum bilhete encontrado</p>
          ) : (
            <div className="space-y-4">
              {bilhetes.map((bilhete, index) => (
                <div key={bilhete.id || index} className="border p-4 rounded-lg">
                  <h3 className="font-semibold">Bilhete #{bilhete.id}</h3>
                  <p><strong>Código:</strong> {bilhete.codigo}</p>
                  <p><strong>Transaction ID:</strong> {bilhete.transaction_id}</p>
                  <p><strong>Status:</strong> {bilhete.status}</p>
                  <p><strong>Valor:</strong> R$ {bilhete.valor}</p>
                  <p><strong>Data:</strong> {bilhete.data} - {bilhete.hora}</p>
                  <p><strong>Apostas:</strong> {bilhete.apostas?.length || 0}</p>
                  
                  {bilhete.apostas && bilhete.apostas.length > 0 && (
                    <div className="mt-2">
                      <strong>Detalhes das apostas:</strong>
                      <ul className="list-disc list-inside ml-4">
                        {bilhete.apostas.map((aposta: any, apostaIndex: number) => (
                          <li key={apostaIndex}>
                            {aposta.jogo} - {aposta.resultado}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
