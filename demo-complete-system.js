// Demonstração completa do sistema automático de pagamentos
import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function demoCompleteSystem() {
  try {
    console.log('🚀 DEMONSTRAÇÃO COMPLETA DO SISTEMA AUTOMÁTICO DE PAGAMENTOS')
    console.log('=' .repeat(70))
    
    await initializeDatabase()
    
    // 1. Criar um bilhete pendente
    console.log('\n📋 PASSO 1: Criando bilhete pendente...')
    const codigoBilhete = `BLT${Date.now()}DEMO`
    const transactionId = `pixi_demo_${Date.now()}`
    
    const result = await executeQuery(`
      INSERT INTO bilhetes (
        codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
        valor_total, quantidade_apostas, status, transaction_id,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pendente', ?, NOW(), NOW())
    `, [
      codigoBilhete,
      1, // ID do usuário admin
      'Administrador',
      '<EMAIL>',
      '00000000000',
      0.50, // Valor de R$ 0,50
      15, // 15 apostas
      transactionId
    ])
    
    console.log(`✅ Bilhete criado: ${codigoBilhete}`)
    console.log(`🔑 Transaction ID: ${transactionId}`)
    console.log(`💰 Valor: R$ 0,50`)
    console.log(`📊 Status inicial: pendente`)
    
    // 2. Aguardar alguns segundos
    console.log('\n⏳ PASSO 2: Aguardando 5 segundos...')
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    // 3. Verificar status via API individual
    console.log('\n🔍 PASSO 3: Verificando status individual...')
    const statusResponse = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${transactionId}`)
    const statusData = await statusResponse.json()
    
    console.log('📊 Resultado da verificação individual:')
    console.log(`   Status: ${statusData.status}`)
    console.log(`   Atualizado: ${statusData.updated ? 'Sim' : 'Não'}`)
    console.log(`   Tempo decorrido: ${statusData.tempo_decorrido}s`)
    
    // 4. Verificar todos os bilhetes pendentes
    console.log('\n🔄 PASSO 4: Verificando todos os bilhetes pendentes...')
    const allPendingResponse = await fetch('http://localhost:3000/api/pix/check-all-pending')
    const allPendingData = await allPendingResponse.json()
    
    console.log('📊 Resultado da verificação em lote:')
    console.log(`   Processados: ${allPendingData.processed}`)
    console.log(`   Atualizados: ${allPendingData.updated}`)
    
    // 5. Verificar status final no banco
    console.log('\n💾 PASSO 5: Verificando status final no banco...')
    const bilheteFinal = await executeQuery(`
      SELECT * FROM bilhetes WHERE codigo = ?
    `, [codigoBilhete])
    
    if (bilheteFinal.length > 0) {
      const bilhete = bilheteFinal[0]
      console.log('📋 Status final no banco:')
      console.log(`   Código: ${bilhete.codigo}`)
      console.log(`   Status: ${bilhete.status}`)
      console.log(`   Valor: R$ ${bilhete.valor_total}`)
      console.log(`   Criado em: ${bilhete.created_at}`)
      console.log(`   Atualizado em: ${bilhete.updated_at}`)
    }
    
    // 6. Testar cron job
    console.log('\n⏰ PASSO 6: Testando cron job...')
    const cronResponse = await fetch('http://localhost:3000/api/cron/check-payments', {
      headers: {
        'Authorization': 'Bearer sistema-bolao-2025-secure'
      }
    })
    const cronData = await cronResponse.json()
    
    console.log('📊 Resultado do cron job:')
    console.log(`   Sucesso: ${cronData.success}`)
    console.log(`   Processados: ${cronData.check_result?.processed || 0}`)
    console.log(`   Atualizados: ${cronData.check_result?.updated || 0}`)
    
    console.log('\n🎉 DEMONSTRAÇÃO CONCLUÍDA!')
    console.log('=' .repeat(70))
    console.log('✅ Sistema automático funcionando perfeitamente!')
    console.log('🔄 Verificação automática: ATIVA')
    console.log('💳 Integração PIX: CONFIGURADA')
    console.log('⏰ Cron job: FUNCIONANDO')
    console.log('📊 Banco de dados: ATUALIZADO')
    
    console.log('\n🎯 PRÓXIMOS PASSOS:')
    console.log('1. Execute: node setup-auto-payment-check.js (para cron automático)')
    console.log('2. Faça apostas no sistema web')
    console.log('3. Os pagamentos serão verificados automaticamente a cada minuto')
    console.log('4. Status será atualizado automaticamente após 30 segundos')
    
    console.log('\n💡 COMANDOS ÚTEIS:')
    console.log('- Verificar todos pendentes: curl -X GET "http://localhost:3000/api/pix/check-all-pending"')
    console.log('- Executar cron manual: curl -X GET "http://localhost:3000/api/cron/check-payments"')
    console.log(`- Marcar como pago manual: curl -X POST "http://localhost:3000/api/v1/MP/webhookruntransation" -H "Content-Type: application/json" -d '{"order_id": "${codigoBilhete}", "status": "PAID", "type": "PIXOUT", "message": "Payment approved"}'`)
    
  } catch (error) {
    console.error('❌ Erro na demonstração:', error)
  }
}

demoCompleteSystem()
