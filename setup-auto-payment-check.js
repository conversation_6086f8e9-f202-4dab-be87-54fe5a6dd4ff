// Script para configurar verificação automática de pagamentos
const cron = require('node-cron')

console.log('🚀 Configurando verificação automática de pagamentos...')

// Verificar a cada 1 minuto
const cronExpression = '*/1 * * * *' // A cada 1 minuto

console.log(`⏰ Agendando verificação para: ${cronExpression}`)

// Função para fazer a verificação
async function checkPayments() {
  try {
    console.log('🔄 Executando verificação automática...')
    
    const response = await fetch('http://localhost:3000/api/cron/check-payments', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer default-secret'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Verificação concluída:', {
        processed: data.check_result?.processed || 0,
        updated: data.check_result?.updated || 0,
        timestamp: new Date().toLocaleString('pt-BR')
      })
      
      if (data.check_result?.updated > 0) {
        console.log(`🎉 ${data.check_result.updated} pagamento(s) confirmado(s) automaticamente!`)
      }
    } else {
      console.error('❌ Erro na verificação:', response.status)
    }
  } catch (error) {
    console.error('❌ Erro na verificação automática:', error.message)
  }
}

// Agendar o cron job
cron.schedule(cronExpression, checkPayments, {
  scheduled: true,
  timezone: "America/Sao_Paulo"
})

console.log('✅ Cron job configurado com sucesso!')
console.log('📊 A verificação será executada a cada 1 minuto')
console.log('🔍 Monitorando bilhetes pendentes...')

// Executar uma vez imediatamente para teste
console.log('🧪 Executando verificação inicial...')
checkPayments()

// Manter o processo rodando
process.on('SIGINT', () => {
  console.log('\n⏹️ Parando verificação automática...')
  process.exit(0)
})

console.log('🎯 Sistema de verificação automática ativo!')
console.log('💡 Para parar, pressione Ctrl+C')
