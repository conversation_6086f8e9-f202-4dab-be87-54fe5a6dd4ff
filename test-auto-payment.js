import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function testAutoPayment() {
  try {
    console.log('🧪 Testando sistema automático de pagamento...')
    
    await initializeDatabase()
    
    // 1. Criar um bilhete de teste
    const codigoBilhete = `BLT${Date.now()}AUTO`
    const transactionId = `pixi_auto_${Date.now()}`
    
    console.log('📋 Criando bilhete de teste:', codigoBilhete)
    
    const result = await executeQuery(`
      INSERT INTO bilhetes (
        codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
        valor_total, quantidade_apostas, status, transaction_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pendente', ?)
    `, [
      codigoBilhete,
      25, // ID do usuário
      'Usuário Auto Test',
      '<EMAIL>',
      '12345678901',
      0.50, // Valor de R$ 0,50
      11, // 11 apostas
      transactionId
    ])
    
    const bilheteId = result.insertId
    console.log(`✅ Bilhete criado com ID: ${bilheteId}`)
    
    // 2. Aguardar 35 segundos para simular o tempo de processamento
    console.log('⏳ Aguardando 35 segundos para simular processamento...')
    await new Promise(resolve => setTimeout(resolve, 35000))
    
    // 3. Testar a API de verificação de status
    console.log('🔍 Testando API de verificação de status...')
    
    const response = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${transactionId}`)
    const statusData = await response.json()
    
    console.log('📊 Resposta da API:', statusData)
    
    if (statusData.success && statusData.updated) {
      console.log('🎉 SUCESSO! Status foi atualizado automaticamente!')
      console.log(`   Status anterior: ${statusData.status_anterior}`)
      console.log(`   Status atual: ${statusData.status}`)
      console.log(`   Tempo decorrido: ${statusData.tempo_decorrido} segundos`)
    } else {
      console.log('⚠️ Status não foi atualizado. Dados:', statusData)
    }
    
    // 4. Verificar no banco se foi atualizado
    const bilheteAtualizado = await executeQuery(`
      SELECT * FROM bilhetes WHERE id = ?
    `, [bilheteId])
    
    if (bilheteAtualizado.length > 0) {
      const bilhete = bilheteAtualizado[0]
      console.log('📋 Status final no banco:', {
        id: bilhete.id,
        codigo: bilhete.codigo,
        status: bilhete.status,
        created_at: bilhete.created_at,
        updated_at: bilhete.updated_at
      })
    }
    
    console.log('\n🎯 TESTE CONCLUÍDO!')
    console.log('💡 Agora o sistema verifica automaticamente o status dos pagamentos!')
    console.log('🔄 A cada 5 segundos, ele consulta a API e atualiza o banco quando necessário.')
    
  } catch (error) {
    console.error('❌ Erro no teste automático:', error)
  }
}

testAutoPayment()
