// Teste rápido da API de verificação de status
async function testStatusCheck() {
  try {
    console.log('🧪 Testando API de verificação de status...')
    
    // Usar um transaction_id existente (do banco de dados)
    const transactionId = 'pixi_01k0k2m021eh08303b6hb0bh37'
    
    console.log(`🔍 Verificando status para: ${transactionId}`)
    
    const response = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${transactionId}`)
    const data = await response.json()
    
    console.log('📊 Resposta da API:')
    console.log(JSON.stringify(data, null, 2))
    
    if (data.success) {
      console.log('\n✅ API funcionando!')
      console.log(`📋 Bilhete: ${data.codigo}`)
      console.log(`💰 Valor: R$ ${data.valor}`)
      console.log(`📊 Status: ${data.status}`)
      console.log(`⏱️ Tempo: ${data.tempo_decorrido}s`)
      console.log(`🔄 Atualizado: ${data.updated ? 'Sim' : 'Não'}`)
    } else {
      console.log('❌ Erro na API:', data.error)
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error)
  }
}

testStatusCheck()
